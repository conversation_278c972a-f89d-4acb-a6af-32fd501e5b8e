<template>
  <section :id="sectionName">

    <app-accordian class="_order-line" v-for="(orderLine, index) in towOrderLinesProxy" group="order-line" :key="orderLine.lOrderLineKey">
      <div class="_thumbnail">
        <app-data-point label="Description">{{ orderLine.vc100Description }}</app-data-point>
        <app-data-point label="Quantity">{{ orderLine.pQty }} <span v-if="Number(orderLine.bCalculated)" slot="affix" title="Calculated value">(Calc.)</span></app-data-point>
        <app-data-point label="Line Total">{{ orderLine.tcTotalPrice | usd }}</app-data-point>
      </div>

      <template slot="controls">
        <app-button @click="removeOrderLine(index)" class="delete-control" :disabled="!canDelete(orderLine)"><i class="far fa-trash-alt"></i></app-button>
        <app-button @click="toggleOrderLines"><i :class="orderLineGroupExpandedIcons"></i></app-button>
      </template>

      <template slot="body">
        <app-grid-form context="inline">
          <div class="columns is-multiline">
            <div class="column is-4">
              <app-text v-model="orderLine.vc100Description" @change="calculateOrderPricing" maxlength="100" :disabled="!canEditDescription(orderLine)">
                Description
              </app-text>
            </div>
            <div class="column is-4">
              <app-number v-model="orderLine.pQty" :min="getMininumQuantity(orderLine.fMinimumQty)" :max="getMaximumQuantity(orderLine.fMaximumQty)" :disabled="!canEditQuantity(orderLine)" @blur="enforceQuantityRange(orderLine)" @change="calculateOrderPricing">
                Quantity <span v-if="Number(orderLine.bCalculated)" title="Calculated value">(Calc.)</span>
              </app-number>
            </div>
            <div class="column is-4">
              <app-select v-model.number="orderLine.lDispatchKey" v-if="dispatchIsVisible(orderLine)" :options="dispatchesProxy" keyAlias="lDispatchKey" valueAlias="DriverTruckPair" :required="true" disabled>
                Dispatch
              </app-select>
            </div>
            <div class="column is-3 is-left">
              <app-checkbox v-model="orderLine.bDiscountable" :disabled="!canEditDiscountable(orderLine)">
                Discountable
              </app-checkbox>
            </div>
            <div class="column is-3">
              <app-checkbox v-model="orderLine.bTaxable" :disabled="!canEditTaxable(orderLine)">
                Taxable
              </app-checkbox>
            </div>
            <div class="column is-3">
              <app-checkbox v-model="orderLine.bCommissionable" :disabled="!canEditCommissionable(orderLine)">
                Commissionable
              </app-checkbox>
            </div>
            <div class="column is-3">
              <app-checkbox v-model="orderLine.bSurchargeable" :disabled="!canEditSurchargeable(orderLine)">
                Surchargeable
              </app-checkbox>
            </div>
            <div class="column is-4 is-left">
              <app-number
                v-model="orderLine.scInitialRate"
                @change="calculateOrderPricing"
                :min="getMinimumRate(orderLine.bNoNegativePrice)"
                :max="getMaximumRate(orderLine.bNoNegativePrice)"
                :disabled="!canOverrideRate(orderLine)">
                Initial <span v-if="orderLine.bInitialFlatRate">Flat</span> Rate
              </app-number>
            </div>
            <div class="column is-4">
              <app-number v-model="orderLine.fInitialQty" :disabled="true">
                Initial Quantity
              </app-number>
            </div>
            <div class="column is-4">
              <app-text v-model="orderLine.sInitialUnit" :disabled="true">
                Initial Unit
              </app-text>
            </div>
            <div class="column is-4 is-left">
              <app-number
                v-model="orderLine.scSecondaryRate"
                @change="calculateOrderPricing"
                :min="getMinimumRate(orderLine.bNoNegativePrice)"
                :max="getMaximumRate(orderLine.bNoNegativePrice)"
                :disabled="!canOverrideRate(orderLine)">
                Secondary <span v-if="orderLine.bSecondaryFlatRate">Flat</span> Rate
              </app-number>
            </div>
            <div class="column is-4">
              <app-number v-model="orderLine.fSecondaryQty" :disabled="true">
                Secondary Quantity
              </app-number>
            </div>
            <div class="column is-4">
              <app-text v-model="orderLine.sSecondaryUnit" :disabled="true">
                Secondary Unit
              </app-text>
            </div>
            <div class="column is-4 is-left">
              <app-number
                v-model="orderLine.scTertiaryRate"
                @change="calculateOrderPricing"
                :min="getMinimumRate(orderLine.bNoNegativePrice)"
                :max="getMaximumRate(orderLine.bNoNegativePrice)"
                :disabled="!canOverrideRate(orderLine)">
                Tertiary <span v-if="orderLine.bTertiaryFlatRate">Flat</span> Rate
              </app-number>
            </div>
            <div class="column is-4">
              <app-number v-model="orderLine.pTertiaryQtyApplied" :disabled="true">
                Tertiary Quantity
              </app-number>
            </div>
            <div class="column is-4">
              <app-text v-model="orderLine.sTertiaryUnit" :disabled="true">
                Tertiary Unit
              </app-text>
            </div>
          </div>
        </app-grid-form>
      </template>
    </app-accordian>

    <div v-if="!towOrderLinesProxy.length">
      <transition name="fade" mode="out-in">
        <app-placeholder v-if="!canGetPricing" key="requirements">
          <p>To add a service, please:</p>
          <br>
          <ul>
            <li :data-complete="isSubterminalSet">
              <i class="_mark" :class="subterminalRequirementsIcon"></i> <span>Select a Company.</span>
            </li>
            <li :data-complete="isCustomerSet">
              <i class="_mark" :class="customerRequirementsIcon"></i> <span>Select a Customer.</span>
            </li>
            <li :data-complete="isTowTypeSet">
              <i class="_mark" :class="towTypeRequirementsIcon"></i> <span>Select a Tow Type.</span>
            </li>
          </ul>
        </app-placeholder>
        <app-placeholder v-else key="empty-state">No pricing items added.</app-placeholder>
      </transition>
    </div>

    <section class="_controls">
      <ServicePicker
        :can-add-pricing="canAddPricing"
        :call="call"
        :dispatches="dispatchesProxy"
        @on-add-service="addService" />

      <app-button
        v-if="!quoteMode"
        @click="getPricing"
        :disabled="!(canGetPricing && isNewCall)">
        Get defaults
      </app-button>

      <div class="flex-space"></div>

      <div v-if="canGetPricing" class="field has-addons is-pulled-right">
        <p class="control width-auto">
          <a class="button is-small is-static">Override Tax</a>
        </p>
        <p class="control width-auto">
          <input v-model="call.fTaxRate_Override" @change="calculateOrderPricing" class="input is-small" type="number" :disabled="!canOverrideTax()" min="0" max="100" style="width: 135px">
        </p>
        <p class="control width-auto">
          <a class="button is-small is-static">%</a>
        </p>
      </div>
    </section>

    <transition name="fade" mode="out-in">
      <div class="_summary" v-if="towOrderLinesProxy.length">
        <section class="_liner" v-if="canSeeLineDetail">
          <div class="_subtotal -label">Subtotal</div>
          <div class="_subtotal -amount">{{ subtotal | usd }}</div>

          <div class="_discount -label">Discount</div>
          <div class="_discount -formula">{{ discountableSubtotal | usd }} x {{ call.fDiscountPct | wholeNumber }}%</div>
          <div class="_discount -amount">{{ call.tcDiscountTotal | usd }}</div>

          <div class="_tax -label">Tax</div>
          <div class="_tax -formula">{{ taxableSubtotal | usd }} x {{ Number(priceRepository.tow.TaxRateOverride || priceRepository.tow.TaxRate) }}%</div>
          <div class="_tax -amount">{{ priceRepository.tow.TaxTotal | usd }}</div>

          <div class="_total -label">Total</div>
          <div class="_total -amount">{{ priceRepository.tow.Total | usd }}</div>
        </section>
      </div>
    </transition>

    <app-grid-form class="comments" context="inline">
      <app-textarea v-model="call.TowOrderComments">
        Invoice Notes
      </app-textarea>
    </app-grid-form>

  </section>
</template>

<script>
import Access from '@/utils/access.js';
import BaseSection from '@/components/call/BaseSection.vue';
import ServicePicker from '@/components/call/PricingSection/ServicePicker.vue' ;

import {
  VALUE_ID,
  AFTER_CALL_READ,
  CALL_SECTION_TOW_PRICING,
  EVENT_RESET_CALL_PRICING,
  EVENT_TOGGLE_ACCORDIAN_GROUP
} from '@/config';

export default {
  name: 'pricing-section',

  extends: BaseSection,

  props: {
    priceRepository: { type: Object }
  },

  components: {
    ServicePicker
  },

  data () {
    return {
      sectionName: CALL_SECTION_TOW_PRICING,

      subtotal: 0,
      taxableSubtotal: 0,
      dispatchDetails: [],
      discountableSubtotal: 0,
      orderLineGroupExpanded: false,
      callSectionDetails: 'towpricing',

      _isCalculating: false,
      _calculateTimeout: null
    };
  },

  computed: {
    towOrderLinesProxy () {
      return this.$_.get(this.call, 'TowOrderLines', []);
    },

    canSeeLineDetail () {
      return !Access.has('prices.restrictOrderLineDetail');
    },

    isSubterminalSet () {
      return Number(this.call.lSubterminalKey) > 0;
    },

    subterminalRequirementsIcon () {
      return {
        'far': true,
        'fa-circle': !this.isSubterminalSet,
        'fa-check-circle': this.isSubterminalSet
      };
    },

    isCustomerSet () {
      return Number(this.call.lCustomerKey) > 0;
    },

    customerRequirementsIcon () {
      return {
        'far': true,
        'fa-circle': !this.isCustomerSet,
        'fa-check-circle': this.isCustomerSet
      };
    },

    isTowTypeSet () {
      return Number(this.call.lTowTypeKey) > 0;
    },

    towTypeRequirementsIcon () {
      return {
        'far': true,
        'fa-circle': !this.isTowTypeSet,
        'fa-check-circle': this.isTowTypeSet
      };
    },

    canGetPricing () {
      return this.isSubterminalSet && this.isCustomerSet && this.isTowTypeSet;
    },

    canAddPricing () {
      return this.canEdit() && this.canGetPricing;
    },

    dispatchesProxy () {
      if (this._isCalculating) return [];

      try {
        let dispatches = this.$_.get(this.call, 'Dispatches', []);

        dispatches.forEach(dispatch => {
          this.$set(dispatch, 'DriverNum', this.getDispatchProperty(dispatch.lDispatchKey, 'DriverNum'));
          this.$set(dispatch, 'TruckNum', this.getDispatchProperty(dispatch.lDispatchKey, 'TruckNum'));

          let driverTruckPair = dispatch.DriverNum
            ? `${dispatch.DriverNum} in truck ${dispatch.TruckNum}`
            : '';

          if (dispatch.bRetow) {
            driverTruckPair = [driverTruckPair, '(Retow)'].join(' ');
          }

          this.$set(dispatch, 'DriverTruckPair', driverTruckPair);
          this.$set(dispatch, 'isEnabled', this.canSelectDispatch(dispatch));
        });

        return dispatches;
      } catch (error) {
        console.error('Error in dispatchesProxy:', error);
        return [];
      }
    },

    orderLineGroupExpandedIcons () {
      return {
        'far': true,
        'fa-chevron-double-down': !this.orderLineGroupExpanded,
        'fa-chevron-double-up': this.orderLineGroupExpanded
      };
    },

    isCallDispatchComplete () {
      return [
        VALUE_ID.callStatus.dispatched,
        VALUE_ID.callStatus.unassigned
      ].includes(this.$_.get(this.call, 'lCallStatusTypeKey', ''));
    }
  },

  watch: {
    towOrderLinesProxy: {
      handler (newValue, oldValue) {
        const newValueWithoutFalsePositives = newValue.map(line => {
          const { dLastModified, lUserKey, uuid, ...rest } = line;
          return rest;
        });

        const oldValueWithoutFalsePositives = oldValue.map(line => {
          const { dLastModified, lUserKey, uuid, ...rest } = line;
          return rest;
        });

        if (
          this.$store.state.call.isTowPricingGuardActive &&
          !this.$_.isEqual(newValueWithoutFalsePositives, oldValueWithoutFalsePositives)
        ) {
          // Todo: Determine source of false positives
          console.log('*** tracked', newValue, oldValue);
          this.$emit('addSpeedBump', 'tow pricing');
        }
      },
      deep: true
    }
  },

  methods: {
    isDispatchPricingType (orderLine) {
      return Number(orderLine.lServicePricingTypeKey) === VALUE_ID.pricingType.dispatch;
    },

    isReconciled (orderLine) {
      return (this.isTowReconciled && !Number(orderLine.bRetow)) || (this.isRetowReconciled && Number(orderLine.bRetow));
    },

    isDispatchReconciled (dispatch) {
      return (this.isTowReconciled && !Number(dispatch.bRetow)) || (this.isRetowReconciled && Number(dispatch.bRetow));
    },

    canEdit(orderLine = null) {
      const isCallTransferredToFES = this.$_.get(this.call, 'bTransferredToFES', false);
      if ([1, '1', true, 'true'].includes(isCallTransferredToFES)) {
        return false;
      }

      const isOrderLineTransferredToFES = this.$_.get(orderLine, 'bTransferredToFES', false);
      if ([1, '1', true, 'true'].includes(isOrderLineTransferredToFES)) {
        return false;
      }

      if (this.isCallConfirmed) {
        return Access.has('calls.fullEditAfterConfirmed');
      }

      if (orderLine) {
        if (this.isDispatchPricingType(orderLine) && this.isReconciled(orderLine)) {
          return Access.has('prices.editDispatchAfterReconciled');
        }
      }

      return true;
    },

    canEditDescription (orderLine) {
      return this.canEdit(orderLine) && Access.has('prices.editDescription');
    },

    canEditQuantity (orderLine) {
      if (Number(orderLine.scInitialRate) > 0 &&
        orderLine.bInitialFlatRate &&
        Number(orderLine.scSecondaryRate) <= 0) {
        return false;
      }

      return this.canEdit(orderLine) && !Number(orderLine.bCalculated);
    },

    canOverrideTax () {
      return this.canEdit() && Access.has('prices.overrideTax');
    },

    canEditDiscountable (orderLine) {
      return this.canEdit(orderLine) && Access.has('prices.editDiscountable');
    },

    canEditTaxable (orderLine) {
      return this.canEdit(orderLine) && Access.has('prices.editTaxable');
    },

    canEditCommissionable (orderLine) {
      return this.canEdit(orderLine) && Access.has('prices.editCommissionable');
    },

    canEditSurchargeable (orderLine) {
      return this.canEdit(orderLine) && Access.has('prices.editSurchargeable');
    },

    canOverrideRate (orderLine) {
      return this.canEdit(orderLine) && (orderLine.bRatesOverridable || Access.has('prices.overrideRates'));
    },

    canDelete (orderLine) {
      // Not using this.canEdit() because of slight difference in handling reconciled calls
      if (this.isCallConfirmed) {
        return Access.has('calls.fullEditAfterConfirmed');
      }

      if (!this.isCallDispatchComplete && Access.has('prices.restrictDelete')) return false;

      if (orderLine) {
        if (this.isDispatchPricingType(orderLine) && this.isReconciled(orderLine)) {
          return Access.has('prices.editDispatchAfterReconciled');
        }
      }

      return true;
    },

    canSelectDispatch (dispatch) {
      if (this.isDispatchReconciled(dispatch)) {
        return Access.has('prices.editDispatchAfterReconciled');
      }

      return true;
    },

    async getPricing (fieldProps = null) {
      const fetchForNewCall = () => {
        return new Promise(resolve => {
          this.$store.dispatch('TOPSCALL__getPricingForNew', {
            data: this.call,
            callback: response => { resolve(response); }
          });
        });
      };

      const fetchForExistingCall = () => {
        return new Promise(resolve => {
          this.$store.dispatch('TOPSCALL__getPricingAfterChange', {
            fieldId: this.$_.get(fieldProps, 'fieldId', ''),
            oldValue: this.$_.get(fieldProps, 'oldValue', ''),
            newValue: this.$_.get(fieldProps, 'newValue', ''),
            keepExistingItems: this.$_.get(fieldProps, 'keepExistingItems', false),
            data: this.call,
            callback: response => { resolve(response); }
          });
        });
      };

      const fetchForQuote = () => {
        return new Promise(resolve => {
          this.$store.dispatch('QUOTE__getPricing', {
            data: this.call,
            callback: response => { resolve(response); }
          });
        });
      };

      if (!this.call.lCustomerKey) return;
      if (!this.call.lTowTypeKey) return;
      if (!this.call.lSubterminalKey) return;

      if (this.$_.isEmpty(this.call.dCallTaken)) {
        this.$set(this.call, 'dCallTaken', await this.getNow());
      }

      let response = null;

      if (this.quoteMode) {
        response = await fetchForQuote();
      } else if (this.isNewCall) {
        response = await fetchForNewCall();

        this.$set(this.call, 'TowOrderLines', []);
        this.$set(this.call, 'DefaultPricingRetrieved', true);
      } else {
        response = await fetchForExistingCall();

        const changes = this.$_.get(response, 'OrderLineChanges', []);
        if (changes.length) {
          this.$notify.info({
            title: 'Info',
            message: changes.join(', ')
          });
        }
      }

      const variantOne = this.$_.get(response, 'TowOrderLines', null);
      const variantTwo = this.$_.get(response, 'OrderLines', null);

      this.$set(this.call, 'TowOrderLines', variantOne || variantTwo);

      this.enhanceOrderLines();
    },

    removeOrderLine (index) {
      this.call.TowOrderLines.splice(index, 1);
      this.calculateTotals();
    },

    calculateTotals() {
      if (this._isCalculating) return;

      try {
        this._isCalculating = true;

        this.$nextTick(() => {
          let newSubtotal = 0;
          let newTaxableSubtotal = 0;
          let newDiscountableSubtotal = 0;

          this.$_.forEach(this.call.TowOrderLines, line => {
            if (line && line.tcTotalPrice) {
              newSubtotal += Number(line.tcTotalPrice);
            }

            if (line && line.bTaxable && line.tcTotalPrice) {
              newTaxableSubtotal += Number(line.tcTotalPrice);
            }

            if (line && line.bDiscountable && line.tcTotalPrice) {
              newDiscountableSubtotal += Number(line.tcTotalPrice);
            }
          });

          this.subtotal = newSubtotal;
          this.taxableSubtotal = newTaxableSubtotal;
          this.discountableSubtotal = newDiscountableSubtotal;

          this._isCalculating = false;
        });
      } catch (error) {
        console.error('Error in calculateTotals:', error);
        this._isCalculating = false;
      }
    },

    canAddService ({ service, dispatchKey = null }) {
      if (service.Type === 'Dispatch' && this.dispatchesProxy.length > 0) {
        const dispatch = this.$_.find(this.dispatchesProxy, ['lDispatchKey', dispatchKey]);

        if (!dispatch) return false;

        // If tow and not reconciled
        if (!this.isTowReconciled && !Number(dispatch.bRetow)) return true;

        // If retow and not reconciled
        if (!this.isRetowReconciled && Number(dispatch.bRetow)) return true;

        // If reconciled
        return Access.has('prices.editDispatchAfterReconciled');
      }

      return true;
    },

    async addService ({ service, dispatchKey }) {
      if (!this.canAddService({ service, dispatchKey })) return;

      service.lDispatchKey = dispatchKey;

      this.createIfMissing('TowOrderLines', []);
      this.call.TowOrderLines.push(service);

      await this.calculateOrderPricing();
      if (this.quoteMode) {
        await this.applyRecalculatedPricing();
      }
    },

    async applyRecalculatedPricing () {
      const fetchRecalculatedPricing = () => {
        return new Promise(resolve => {
          const action = this.quoteMode ? 'QUOTE__recalculatePricing' : 'TOPSCALL__recalculatePricing';
          this.$store.dispatch(action, {
            call: this.call,
            success: (response) => { resolve(response); }
          });
        });
      };

      const response = await fetchRecalculatedPricing();

      if ('TowOrderLines' in response) {
        this.call.TowOrderLines = response.TowOrderLines;
      }
    },

    async calculateOrderPricing () {
      if (this._isCalculating) return true;

      try {
        this._isCalculating = true;

        const fetchForNewCall = () => {
          return new Promise(resolve => {
            this.$store.dispatch('TOPSCALL__getOrderPricingForNew', {
              towTypeKey: this.call.lTowTypeKey,
              subterminalKey: this.call.lSubterminalKey,
              customerKey: this.call.lCustomerKey,
              taxRateOverride: this.call.fTaxRate_Override,
              discountPercent: this.call.fDiscountPct,
              orderLines: this.call.TowOrderLines,
              callback: response => { resolve(response); }
            });
          });
        };

        const fetchForExistingCall = () => {
          return new Promise(resolve => {
            this.$store.dispatch('TOPSCALL__getOrderPricing', {
              callKey: this.callKey,
              orderType: 'T',
              taxRateOverride: this.call.fTaxRate_Override,
              orderLines: this.call.TowOrderLines,
              callback: response => { resolve(response); }
            });
          });
        };

        if ((this.$_.get(this.call, 'TowOrderLines', [])).length === 0) {
          this._isCalculating = false;
          return true;
        }

        try {
          let response = null;

          if (this.isNewCall) {
            response = await fetchForNewCall();
          } else {
            response = await fetchForExistingCall();
          }

          const variantOne = this.$_.get(response, 'TowOrderLines', null);
          const variantTwo = this.$_.get(response, 'OrderLines', null);

          this.$set(this.call, 'TowOrderLines', variantOne || variantTwo);

          this.priceRepository.tow.TaxRateOverride = response.TaxRateOverride;
          this.priceRepository.tow.TaxRate = response.TaxRate;
          this.priceRepository.tow.TaxTotal = response.TaxTotal;
          this.priceRepository.tow.TaxExempt = response.TaxExempt;

          this.priceRepository.tow.Total = response.Total;
          this.priceRepository.tow.DiscountTotal = response.DiscountTotal;
          this.priceRepository.tow.DiscountPct = response.DiscountPct;

          this.enhanceOrderLines();
          this.calculateTotals();

          this._isCalculating = false;
          return true;
        } catch (error) {
          this._isCalculating = false;
          throw error;
        }
      } catch (error) {
        this._isCalculating = false;
        throw error;
      }
    },

    enforceQuantityRange (orderLine) {
      if (orderLine.pQty < this.getMininumQuantity(orderLine.fMinimumQty)) {
        orderLine.pQty = this.getMininumQuantity(orderLine.fMinimumQty);
      }

      if (orderLine.pQty > this.getMaximumQuantity(orderLine.fMaximumQty)) {
        orderLine.pQty = this.getMaximumQuantity(orderLine.fMaximumQty);
      }
    },

    getMininumQuantity (value) {
      return this.$_.isEmpty(value) ? 0 : Number(value);
    },

    getMaximumQuantity (value) {
      return this.$_.isEmpty(value) ? 99999999 : Number(value);
    },

    enforceRateRange (orderLine, fieldId) {
      if (orderLine[fieldId] < this.getMinimumRate(orderLine.bNoNegativePrice)) {
        orderLine[fieldId] = this.getMinimumRate(orderLine.bNoNegativePrice);
      }

      if (orderLine[fieldId] > this.getMaximumRate(orderLine.bNoNegativePrice)) {
        orderLine[fieldId] = this.getMaximumRate(orderLine.bNoNegativePrice);
      }
    },

    getMinimumRate (allowNegativeValue) {
      if (Access.has('prices.bypassPricingPolarity')) return null;

      // Convert double negative in bNoNegativePrice for easier reading
      // bNoNegativePrice = true means disallow negative value
      // bNoNegativePrice = false means allow negative value
      allowNegativeValue = !allowNegativeValue;

      return allowNegativeValue ? null : 0;
    },

    getMaximumRate (allowPositiveValue) {
      if (Access.has('prices.bypassPricingPolarity')) return null;

      // Convert double negative in bNoNegativePrice for easier reading
      // bNoNegativePrice = true means disallow positive value
      // bNoNegativePrice = false means allow positive value

      return allowPositiveValue ? null : 0;
    },

    enhanceOrderLines () {
      // Use a negative order line key for new items.
      // This is necessary to remove items.
      if (this.call.TowOrderLines) {
        let minimumKey = Math.min(...this.call.TowOrderLines.map(line => Number(line.lOrderLineKey))) - 1;

        this.$_.forEach(this.call.TowOrderLines, line => {
          if (line.lOrderLineKey === '') {
            line.lOrderLineKey = minimumKey;
            minimumKey--;
          }
        });
      }
    },

    afterCallRead () {
      if (this._isCalculating) return;

      try {
        this._isCalculating = true;

        this.enhanceOrderLines();
        this.calculateTotals();

        this.$nextTick(() => {
          this.getDispatchDetails();

          if (this._calculateTimeout) {
            clearTimeout(this._calculateTimeout);
          }

          // Use a longer timeout to ensure other components have finished processing
          this._calculateTimeout = setTimeout(() => {
            this._isCalculating = false;
            this.calculateOrderPricing();
          }, 500);
        });
      } catch (error) {
        console.error('Error in afterCallRead:', error);
        this._isCalculating = false;
      }
    },

    getDispatchProperty (dispatchKey, property) {
      let dispatch = this.$_.find(this.dispatchDetails, ['Key', dispatchKey]);

      if (!dispatch) return;

      return this.$_.get(dispatch, property, '');
    },

    getDispatchDetails () {
      if (!this.callKey) return;

      this.$store.dispatch('CALL__getDispatchDetails', {
        callKey: this.callKey,
        callback: response => {
          this.dispatchDetails = response;
        }
      });
    },

    dispatchIsVisible (orderLine) {
      return this.dispatchesProxy.length &&
        this.dispatchDetails.length &&
        this.isDispatchPricingType(orderLine);
    },

    toggleOrderLines () {
      this.orderLineGroupExpanded = !this.orderLineGroupExpanded;

      this.$hub.$emit(EVENT_TOGGLE_ACCORDIAN_GROUP, {
        group: 'order-line',
        expanded: this.orderLineGroupExpanded
      });
    }
  },

  mounted () {
    this.$hub.$on(AFTER_CALL_READ, () => {
      setTimeout(() => {
        this.createIfMissing('TowOrderComments', '');
      }, 300);

      this.afterCallRead();
    });

    this.$hub.$on(EVENT_RESET_CALL_PRICING, props => {
      this.getPricing(props);
    });
  },

  beforeDestroy () {
    this.call.lTowTypeKey = null;
  },

  provide () {
    return {
      isNewCall: () => this.isNewCall,
      isTowReconciled: () => this.isTowReconciled,
      isRetowReconciled: () => this.isRetowReconciled,
      canAddService: this.canAddService
    };
  }
};
</script>
